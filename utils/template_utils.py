import os

def load_template(template_name, template_type='generators'):
    template_path = os.path.join(
        os.path.dirname(os.path.dirname(__file__)), 
        'templates', 
        template_type, 
        template_name
    )
    
    with open(template_path, 'r', encoding='utf-8') as f:
        return f.read()

def render_template(template_content, **kwargs):
    result = template_content
    for key, value in kwargs.items():
        placeholder = '{' + key + '}'
        result = result.replace(placeholder, str(value))
    return result

def load_and_render_template(template_name, template_type='generators', **kwargs):
    template_content = load_template(template_name, template_type)
    return render_template(template_content, **kwargs)
