import os
import re

def extract_number_from_filename(filename):
    match = re.search(r'\d+', filename)
    return int(match.group()) if match else 0

def sort_files_numerically(filename):
    name, _ = os.path.splitext(filename)
    return int(name) if name.isdigit() else name

def get_files_with_extensions(directory, extensions):
    if not os.path.exists(directory):
        return []
    
    return [f for f in os.listdir(directory) 
            if f.lower().endswith(extensions)]

def get_txt_files_sorted(directory):
    files = [f for f in os.listdir(directory) if f.endswith(".txt")]
    files.sort(key=extract_number_from_filename)
    return files

def get_image_files_sorted(directory):
    valid_exts = ('.jpg', '.jpeg', '.png', '.gif', '.bmp', '.webp')
    files = get_files_with_extensions(directory, valid_exts)
    files.sort(key=sort_files_numerically)
    return files

def get_jpg_files_sorted(directory):
    if not os.path.exists(directory):
        return []
    
    jpg_files = [f for f in os.listdir(directory) if f.endswith('.jpg')]
    jpg_files_sorted = sorted(jpg_files, key=lambda x: int(os.path.splitext(x)[0]))
    return jpg_files_sorted
