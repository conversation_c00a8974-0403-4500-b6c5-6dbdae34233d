import os
from config import config

def calculate_job_paths(job_id):
    if not job_id:
        raise ValueError("Job ID is required")
    
    output_base_dir = config.OUTPUT_BASE_DIR
    upload_folder = config.UPLOAD_FOLDER
    job_base = os.path.join(output_base_dir, job_id)
    html_dir = os.path.join(job_base, 'html')
    
    return {
        'job_id': job_id,
        'job_base': job_base,
        'html_dir': html_dir,
        'image_dir': os.path.join(html_dir, 'image'),
        'txt_dir': os.path.join(html_dir, 'txt'),
        'zip_dl_dir': os.path.join(job_base, 'zip', 'dl'),
        'pdf_path': os.path.join(upload_folder, f"{job_id}.pdf")
    }
