services:
  pdf-converter:
    build: .
    volumes:
      - ./templates:/app/templates:ro
      - ./static:/app/static:ro
      - pdf_output:/app/output
    env_file:
      - env.local
    restart: unless-stopped
    networks:
      - pdf-network

  nginx:
    image: nginx:alpine
    ports:
      - "80:80"
      - "443:443"
    volumes:
      - ./nginx.local.conf:/etc/nginx/nginx.conf:ro
      - pdf_output:/var/www/html:ro
      - ./ssl:/etc/nginx/ssl:ro
    depends_on:
      - pdf-converter
    restart: unless-stopped
    networks:
      - pdf-network

networks:
  pdf-network:
    driver: bridge

volumes:
  pdf_output:
