function saveEditorText() {
  var newContent = document.getElementById("editor-text").value;
  var jobId = window.opener.JOB_ID || "unknown";
  
  if (!jobId || jobId === "unknown") {
    alert("ジョブIDが検出できません");
    return;
  }
  
  var formData = new FormData();
  formData.append("id", window.EDITOR_ID);
  formData.append("content", newContent);
  
  var serverUrl = "";
  if (window.location.protocol === "file:") {
    serverUrl = "http://127.0.0.1:5000";
  } else if (window.location.hostname === "localhost" || window.location.hostname === "127.0.0.1") {
    serverUrl = "";
  }
  
  fetch(serverUrl + "/update_text/" + jobId, { 
    method: "POST", 
    body: formData 
  })
  .then(function(response) { 
    return response.json(); 
  })
  .then(function(data) {
    if (data.success) {
      alert("保存完了");
      window.opener.location.reload();
      window.close();
    } else { 
      alert("保存失敗: " + (data.error || data.message)); 
    }
  })
  .catch(function(error) { 
    alert("エラー: " + error); 
  });
}
