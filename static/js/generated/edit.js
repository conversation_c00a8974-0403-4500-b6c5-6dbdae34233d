function toggleText(textId, btnId) {
  var container = document.getElementById(textId);
  var btn = document.getElementById(btnId);
  
  if (!container || !btn) {
    return;
  }
  
  var isExpanded = btn.dataset.expanded === 'true';
  
  if (!isExpanded) {
     container.style.maxHeight = 'none';
     container.style.overflow = 'visible';
     btn.dataset.expanded = 'true';
     btn.innerText = '－';
  } else {
     container.style.maxHeight = '200px';
     container.style.overflow = 'auto';
     btn.dataset.expanded = 'false';
     btn.innerText = '＋';
  }
}

function updateToggleButtons() {
  var textBoxes = document.querySelectorAll('.col4');
  textBoxes.forEach(function(box) {
      var btn = box.querySelector('.toggle-btn');
      if (!btn) return;
      
      var hasOverflow = box.scrollHeight > box.clientHeight;
      
      if(box.style.maxHeight === 'none' || hasOverflow) {
          btn.style.display = 'block';
      } else {
          btn.style.display = 'none';
      }
  });
}

function makeEditable(id) {
  var textDiv = document.getElementById('text-content-' + id);
  if (!textDiv) {
    return;
  }
  textDiv.contentEditable = 'true';
  textDiv.focus();
  textDiv.style.backgroundColor = '#f9f9f9';
  textDiv.style.border = '2px solid #007BFF';
  textDiv.style.borderRadius = '4px';
}

function makeReadonly(id) {
  var textDiv = document.getElementById('text-content-' + id);
  if (!textDiv) return;
  textDiv.contentEditable = 'false';
  textDiv.style.backgroundColor = 'transparent';
  textDiv.style.border = 'none';
}

function saveText(id) {
  var textDiv = document.getElementById('text-content-' + id);
  if (!textDiv) {
    alert('エラー: テキスト要素が見つかりません');
    return;
  }
  
  var textContent = textDiv.innerText || textDiv.textContent;
  
  var formData = new FormData();
  formData.append('id', id);
  formData.append('content', textContent);
  
  var saveButtons = document.querySelectorAll('button[onclick*="saveText(\'' + id + '\')"]');
  var saveBtn = null;
  for (var i = 0; i < saveButtons.length; i++) {
    if (!saveButtons[i].disabled) {
      saveBtn = saveButtons[i];
      break;
    }
  }
  
  if (saveBtn) {
    saveBtn.innerText = '保存中...';
    saveBtn.disabled = true;
  }
  
  if (!JOB_ID || JOB_ID === 'unknown') {
    alert('ジョブIDが検出できません。ページをリロードしてください。');
    if (saveBtn) {
      saveBtn.innerText = '保存';
      saveBtn.disabled = false;
    }
    return;
  }
  
  var fetchUrl = '/update_text/' + JOB_ID;
  if (SERVER_URL) {
    fetchUrl = SERVER_URL + '/update_text/' + JOB_ID;
  }
  
  fetch(fetchUrl, {
    method: 'POST',
    body: formData
  })
  .then(function(response) {
    return response.json();
  })
  .then(function(data) {
    if (data.success) {
      if (saveBtn) {
        saveBtn.innerText = '保存済み';
        saveBtn.style.backgroundColor = '#28a745';
        saveBtn.disabled = false;
        setTimeout(function() {
          saveBtn.innerText = '保存';
          saveBtn.style.backgroundColor = '#28a745';
        }, 2000);
      }
      updateToggleButtons();
    } else {
      alert('保存に失敗しました: ' + (data.error || data.message));
      if (saveBtn) {
        saveBtn.innerText = '保存';
        saveBtn.disabled = false;
      }
    }
  })
  .catch(function(error) {
    alert('保存エラーが発生しました: ' + error.message);
    if (saveBtn) {
      saveBtn.innerText = '保存';
      saveBtn.disabled = false;
    }
  });
}

function openEditor(id, imgPath) {
  var textDiv = document.getElementById('text-content-' + id);
  if (!textDiv) {
    alert('エラー: テキスト要素が見つかりません');
    return;
  }
  var content = textDiv.innerText || textDiv.textContent;
  var newWindow = window.open("", "_blank", "width=800,height=600");

  var editorHtml = generateEditorHTML(id, imgPath, content);
  newWindow.document.write(editorHtml);
  newWindow.document.close();
}

function generateEditorHTML(id, imgPath, content) {
  var safeContent = content.replace(/&/g, '&amp;').replace(/</g, '&lt;').replace(/>/g, '&gt;').replace(/"/g, '&quot;').replace(/'/g, '&#39;');

  var html = '<!DOCTYPE html><html lang="ja"><head><meta charset="UTF-8"><title>テキストエディター</title>';
  html += '<link rel="stylesheet" href="../../../static/css/generated/editor.css">';
  html += '</head><body>';
  html += '<h1>テキストエディター</h1>';
  if(imgPath && imgPath !== '') {
      html += '<div class="editor-image"><a href="' + imgPath + '" target="_blank"><img src="' + imgPath + '"></a></div>';
  }
  html += '<div class="editor-controls"><button onclick="saveEditorText()">保存</button><button onclick="window.close()">閉じる</button></div>';
  html += '<textarea id="editor-text">' + safeContent + '</textarea>';
  html += '<script>';
  html += 'window.EDITOR_ID = "' + id + '";';
  html += 'function saveEditorText() {';
  html += '  var newContent = document.getElementById("editor-text").value;';
  html += '  var jobId = window.opener.JOB_ID || "unknown";';
  html += '  if (!jobId || jobId === "unknown") { alert("ジョブIDが検出できません"); return; }';
  html += '  var formData = new FormData();';
  html += '  formData.append("id", "' + id + '");';
  html += '  formData.append("content", newContent);';
  html += '  var serverUrl = "";';
  html += '  if (window.location.protocol === "file:") {';
  html += '    serverUrl = "http://127.0.0.1:5000";';
  html += '  } else if (window.location.hostname === "localhost" || window.location.hostname === "127.0.0.1") {';
  html += '    serverUrl = "";';
  html += '  } else {';
  html += '  }';
  html += '  fetch(serverUrl + "/update_text/" + jobId, { method: "POST", body: formData })';
  html += '    .then(function(response) { return response.json(); })';
  html += '    .then(function(data) {';
  html += '      if (data.success) {';
  html += '        alert("保存完了");';
  html += '        window.opener.location.reload();';
  html += '        window.close();';
  html += '      } else { alert("保存失敗: " + (data.error || data.message)); }';
  html += '    })';
  html += '    .catch(function(error) { alert("エラー: " + error); });';
  html += '}';
  html += '</script>';
  html += '</body></html>';
  return html;
}

window.onload = function() {
  updateToggleButtons();
};

window.onresize = function() {
  updateToggleButtons();
};
