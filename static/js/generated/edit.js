function toggleText(textId, btnId) {
  var container = document.getElementById(textId);
  var btn = document.getElementById(btnId);
  
  if (!container || !btn) {
    return;
  }
  
  var isExpanded = btn.dataset.expanded === 'true';
  
  if (!isExpanded) {
     container.style.maxHeight = 'none';
     container.style.overflow = 'visible';
     btn.dataset.expanded = 'true';
     btn.innerText = '－';
  } else {
     container.style.maxHeight = '200px';
     container.style.overflow = 'auto';
     btn.dataset.expanded = 'false';
     btn.innerText = '＋';
  }
}

function updateToggleButtons() {
  var textBoxes = document.querySelectorAll('.col4');
  textBoxes.forEach(function(box) {
      var btn = box.querySelector('.toggle-btn');
      if (!btn) return;
      
      var hasOverflow = box.scrollHeight > box.clientHeight;
      
      if(box.style.maxHeight === 'none' || hasOverflow) {
          btn.style.display = 'block';
      } else {
          btn.style.display = 'none';
      }
  });
}

function makeEditable(id) {
  var textDiv = document.getElementById('text-content-' + id);
  if (!textDiv) {
    return;
  }
  textDiv.contentEditable = 'true';
  textDiv.focus();
  textDiv.style.backgroundColor = '#f9f9f9';
  textDiv.style.border = '2px solid #007BFF';
  textDiv.style.borderRadius = '4px';
}

function makeReadonly(id) {
  var textDiv = document.getElementById('text-content-' + id);
  if (!textDiv) return;
  textDiv.contentEditable = 'false';
  textDiv.style.backgroundColor = 'transparent';
  textDiv.style.border = 'none';
}

function saveText(id) {
  var textDiv = document.getElementById('text-content-' + id);
  if (!textDiv) {
    alert('エラー: テキスト要素が見つかりません');
    return;
  }
  
  var textContent = textDiv.innerText || textDiv.textContent;
  
  var formData = new FormData();
  formData.append('id', id);
  formData.append('content', textContent);
  
  var saveButtons = document.querySelectorAll('button[onclick*="saveText(\'' + id + '\')"]');
  var saveBtn = null;
  for (var i = 0; i < saveButtons.length; i++) {
    if (!saveButtons[i].disabled) {
      saveBtn = saveButtons[i];
      break;
    }
  }
  
  if (saveBtn) {
    saveBtn.innerText = '保存中...';
    saveBtn.disabled = true;
  }
  
  if (!JOB_ID || JOB_ID === 'unknown') {
    alert('ジョブIDが検出できません。ページをリロードしてください。');
    if (saveBtn) {
      saveBtn.innerText = '保存';
      saveBtn.disabled = false;
    }
    return;
  }
  
  var fetchUrl = '/update_text/' + JOB_ID;
  if (SERVER_URL) {
    fetchUrl = SERVER_URL + '/update_text/' + JOB_ID;
  }
  
  fetch(fetchUrl, {
    method: 'POST',
    body: formData
  })
  .then(function(response) {
    return response.json();
  })
  .then(function(data) {
    if (data.success) {
      if (saveBtn) {
        saveBtn.innerText = '保存済み';
        saveBtn.style.backgroundColor = '#28a745';
        saveBtn.disabled = false;
        setTimeout(function() {
          saveBtn.innerText = '保存';
          saveBtn.style.backgroundColor = '#28a745';
        }, 2000);
      }
      updateToggleButtons();
    } else {
      alert('保存に失敗しました: ' + (data.error || data.message));
      if (saveBtn) {
        saveBtn.innerText = '保存';
        saveBtn.disabled = false;
      }
    }
  })
  .catch(function(error) {
    alert('保存エラーが発生しました: ' + error.message);
    if (saveBtn) {
      saveBtn.innerText = '保存';
      saveBtn.disabled = false;
    }
  });
}

function openEditor(id, imgPath) {
  var textDiv = document.getElementById('text-content-' + id);
  if (!textDiv) {
    alert('エラー: テキスト要素が見つかりません');
    return;
  }
  var content = textDiv.innerText || textDiv.textContent;
  var newWindow = window.open("", "_blank", "width=800,height=600");

  generateEditorHTML(id, imgPath, content)
    .then(function(editorHtml) {
      newWindow.document.write(editorHtml);
      newWindow.document.close();
    })
    .catch(function(error) {
      console.error('Failed to load editor template:', error);
      newWindow.close();
      alert('エディターの読み込みに失敗しました: ' + error.message);
    });
}

// Cache for the template
var editorTemplateCache = null;

function loadEditorTemplate() {
  if (editorTemplateCache) {
    return Promise.resolve(editorTemplateCache);
  }

  // Determine the correct path based on current location
  var templatePath = '/static/templates/editor.html';
  if (window.location.protocol === 'file:') {
    templatePath = '../../../static/templates/editor.html';
  }

  return fetch(templatePath)
    .then(function(response) {
      if (!response.ok) {
        throw new Error('Failed to load editor template');
      }
      return response.text();
    })
    .then(function(template) {
      editorTemplateCache = template;
      return template;
    });
}

function generateEditorHTML(id, imgPath, content) {
  var safeContent = content.replace(/&/g, '&amp;').replace(/</g, '&lt;').replace(/>/g, '&gt;').replace(/"/g, '&quot;').replace(/'/g, '&#39;');

  return loadEditorTemplate()
    .then(function(template) {
      var imageSection = '';
      if (imgPath && imgPath !== '') {
        imageSection = '<div class="editor-image"><a href="' + imgPath + '" target="_blank"><img src="' + imgPath + '"></a></div>';
      }

      // Determine correct paths based on current location
      var cssPath, jsPath;
      if (window.location.protocol === 'file:') {
        cssPath = '../../../static/css/generated/editor.css';
        jsPath = '../../../static/js/generated/editor.js';
      } else {
        cssPath = '/static/css/generated/editor.css';
        jsPath = '/static/js/generated/editor.js';
      }

      var html = template
        .replace(/\{\{EDITOR_ID\}\}/g, id)
        .replace(/\{\{CONTENT\}\}/g, safeContent)
        .replace(/\{\{IMAGE_SECTION\}\}/g, imageSection)
        .replace(/\{\{CSS_PATH\}\}/g, cssPath)
        .replace(/\{\{JS_PATH\}\}/g, jsPath);

      return html;
    });
}

window.onload = function() {
  updateToggleButtons();
};

window.onresize = function() {
  updateToggleButtons();
};
