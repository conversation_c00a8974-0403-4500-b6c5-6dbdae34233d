document.addEventListener('DOMContentLoaded', function() {
  initializeKeyboardShortcuts();
  initializeBackToTop();
  initializeImageZoomAndDrag();
  initializeSliderAutoHide();
});

function initializeKeyboardShortcuts() {
  document.addEventListener('keydown', function(event) {
    if (event.key === 'ArrowLeft') {
      var prevLink = document.querySelector('a[rel="prev"]');
      if (prevLink) { window.location.href = prevLink.href; }
    }
    if (event.key === 'ArrowRight') {
      var nextLink = document.querySelector('a[rel="next"]');
      if (nextLink) { window.location.href = nextLink.href; }
    }
  });
}

function initializeBackToTop() {
  window.onscroll = function() {
    var backToTop = document.getElementById("back-to-top");
    if (document.body.scrollTop > 100 || document.documentElement.scrollTop > 100) {
      backToTop.style.display = "block";
    } else {
      backToTop.style.display = "none";
    }
  };
  
  var backToTopBtn = document.getElementById("back-to-top");
  if (backToTopBtn) {
    backToTopBtn.addEventListener("click", function(e) {
      e.preventDefault();
      window.scrollTo({top: 0, behavior: 'smooth'});
    });
  }
}

function initializeImageZoomAndDrag() {
  var slider = document.getElementById("zoom-slider");
  var currentImage = document.getElementById("current-image");

  if (!slider || !currentImage) return;

  var currentScale = slider ? parseFloat(slider.value) : 1;
  var currentTranslateX = 0;
  var currentTranslateY = 0;

  function updateTransform() {
    currentImage.style.transform = "translate(" + currentTranslateX + "px, " + currentTranslateY + "px) scale(" + currentScale + ")";
  }

  slider.addEventListener("input", function() {
    currentScale = parseFloat(this.value);
    updateTransform();
  });

  var isDragging = false;
  var dragStartX = 0;
  var dragStartY = 0;

  currentImage.addEventListener("mousedown", function(e) {
    isDragging = true;
    dragStartX = e.clientX;
    dragStartY = e.clientY;
    e.preventDefault();
  });

  document.addEventListener("mousemove", function(e) {
    if (isDragging) {
      var deltaX = e.clientX - dragStartX;
      var deltaY = e.clientY - dragStartY;
      dragStartX = e.clientX;
      dragStartY = e.clientY;
      currentTranslateX += deltaX;
      currentTranslateY += deltaY;
      updateTransform();
    }
  });

  document.addEventListener("mouseup", function(e) {
    if (isDragging) {
      isDragging = false;
    }
  });

  currentImage.addEventListener("touchstart", function(e) {
    isDragging = true;
    var touch = e.touches[0];
    dragStartX = touch.clientX;
    dragStartY = touch.clientY;
  });

  document.addEventListener("touchmove", function(e) {
    if (isDragging) {
      var touch = e.touches[0];
      var deltaX = touch.clientX - dragStartX;
      var deltaY = touch.clientY - dragStartY;
      dragStartX = touch.clientX;
      dragStartY = touch.clientY;
      currentTranslateX += deltaX;
      currentTranslateY += deltaY;
      updateTransform();
    }
  });

  document.addEventListener("touchend", function(e) {
    if (isDragging) {
      isDragging = false;
    }
  });
}

function initializeSliderAutoHide() {
  var sliderContainer = document.getElementById("zoom-slider-container");
  var imageContainer = document.getElementById("image-container");
  
  if (!sliderContainer || !imageContainer) return;
  
  var sliderTimeout;
  
  function hideSlider() {
    sliderContainer.style.opacity = "0";
  }
  
  function showSlider() {
    sliderContainer.style.opacity = "0.8";
    clearTimeout(sliderTimeout);
    sliderTimeout = setTimeout(hideSlider, 3000);
  }
  
  sliderTimeout = setTimeout(hideSlider, 3000);
  imageContainer.addEventListener("mouseover", showSlider);
  imageContainer.addEventListener("mouseleave", function() {
    sliderTimeout = setTimeout(hideSlider, 3000);
  });
}
