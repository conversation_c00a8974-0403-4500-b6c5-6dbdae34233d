html {
  scroll-behavior: smooth;
}

body {
  background-color: #f3f3f3;
}

a {
  text-decoration: none;
  color: #181818;
  padding: 5px;
}

a:hover {
  background-color: #d8f6ff;
}

.text-box {
  background-color: #ffffff;
  padding: 32px;
  margin: 50px auto;
  display: inline-block;
  text-align: left;
  max-width: 80%;
  font-size: 103%;
  line-height: 1.65;
}

.textbox-header {
  color: #5a5a5a;
}

#back-to-top {
  position: fixed;
  bottom: 30px;
  right: 30px;
  width: 50px;
  height: 50px;
  background-color: rgba(173,216,230,0.7);
  color: #fff;
  text-align: center;
  line-height: 50px;
  border-radius: 50%;
  font-size: 24px;
  display: none;
  z-index: 1000;
  transition: opacity 0.3s;
  box-shadow: 0 4px 8px rgba(0,0,0,0.3);
}

#back-to-top:hover {
  opacity: 0.9;
}

/* 画像ズーム・ドラッグ用 */
#current-image {
  transition: transform 0.2s ease;
  backface-visibility: hidden;
  cursor: move;
}

/* 矢印ナビゲーション用 */
.nav-arrow {
  position: absolute;
  top: 50%;
  transform: translateY(-50%);
  cursor: pointer;
  display: none;
  font-size: 2em;
  color: rgba(0, 0, 0, 0.5);
  text-decoration: none;
  user-select: none;
}

#image-container:hover .nav-arrow {
  display: block;
}

.left-arrow {
  left: 10px;
}

.right-arrow {
  right: 10px;
}

.image-container {
  position: relative;
  display: inline-block;
}

.zoom-slider-container {
  position: absolute;
  bottom: -30px;
  left: 50%;
  transform: translateX(-50%);
  z-index: 9999;
  opacity: 0.8;
  transition: opacity 0.5s;
}

.zoom-label-minus,
.zoom-label-plus {
  margin: 0 10px;
}

.zoom-slider {
  vertical-align: middle;
}

.nav-links-top {
  margin-top: 40px;
  text-align: center;
}

.nav-links-bottom {
  margin-top: 10px;
  text-align: center;
}

.page-indicator {
  margin-left: 10px;
}
