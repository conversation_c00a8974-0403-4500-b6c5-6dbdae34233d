html {
  scroll-behavior: smooth;
}

body {
  background-color: #e9e9e9;
  font-family: Arial, sans-serif;
}

.container {
  max-width: 90%;
  margin: 0 auto;
}

.title {
  text-align: center;
  font-size: 2em;
  margin: 20px 0;
}

.box {
  background-color: #fff;
  border: 1px solid #ccc;
  padding: 50px;
  margin: 20px auto;
  box-shadow: 0 4px 8px rgba(0,0,0,0.3);
  text-align: left;
}

ol {
  padding-left: 20px;
}

li {
  margin-bottom: 10px;
}

a {
  text-decoration: none;
  color: #1f1f1f;
  display: block;
  padding: 5px;
  font-size: 112%; /* 文字サイズを 8% 拡大 */
}

a:hover {
  background-color: #d8f6ff;
}

/* 上に戻るボタン */
#back-to-top {
  position: fixed;
  bottom: 30px;
  right: 30px;
  width: 50px;
  height: 50px;
  background-color: rgba(173,216,230,0.7);
  color: #fff;
  text-align: center;
  line-height: 50px;
  border-radius: 50%;
  text-decoration: none;
  font-size: 24px;
  display: none;
  z-index: 1000;
  transition: opacity 0.3s;
}

#back-to-top:hover {
  opacity: 0.9;
}

.nav-bar {
  text-align: center;
  margin: 20px 0;
}

.nav-bar .nav-link {
  margin-right: 20px;
  text-decoration: none;
  color: #007BFF;
}

.nav-bar .nav-link:hover {
  text-decoration: underline;
}

.nav-bar .page-count {
  margin-top: 10px;
  color: #666;
}
