body {
  font-family: Arial, sans-serif;
  background-color: #f0f2f5;
  margin: 0;
  padding: 0;
}

h1 {
  text-align: center;
  padding: 20px 0;
  margin: 0;
  background-color: #007BFF;
  color: #fff;
}

.nav-section {
  text-align: center;
  padding: 10px 0;
  background-color: #f8f9fa;
  border-bottom: 1px solid #e0e0e0;
}

.nav-link {
  text-decoration: none;
  color: #007BFF;
  margin: 0 15px;
  padding: 8px 12px;
  border-radius: 3px;
  transition: background-color 0.3s;
}

.nav-link:hover {
  background-color: #e9ecef;
  text-decoration: none;
}

.container {
  margin: 0 7%;
  padding: 20px;
}

.row {
  display: flex;
  align-items: stretch;
  background: #fff;
  border-radius: 5px;
  margin-bottom: 15px;
  box-shadow: 0 2px 5px rgba(0,0,0,0.1);
  overflow: hidden;
}

.col {
  border-right: 1px solid #e0e0e0;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 10px;
}

.col:last-child {
  border-right: none;
  position: relative;
  padding: 10px;
}

.col1 { width: 65px; }
.col2 { width: 250px; }
.col3 { width: 70px; }

.col4 {
  flex: 1; 
  min-height: 130px; 
  max-height: 200px; 
  overflow-y: auto; 
  position: relative;
  text-align: left;
  padding-bottom: 40px;
}

.col2 img {
  max-width: 100%;
  max-height: 100%;
  box-shadow: 0 2px 4px rgba(0,0,0,0.3);
}

.text-content {
  padding: 5px;
  margin: 0;
  white-space: pre-wrap;
  text-align: left;
  cursor: text;
  min-height: 20px;
}

.text-content.editing {
  background-color: #f9f9f9;
  border: 2px solid #007BFF;
  border-radius: 4px;
}

.controls {
  position: absolute;
  bottom: 5px;
  z-index: 10;
}

.left-controls {
  left: 5px;
}

.right-controls {
  right: 5px;
}

[id^="text-content-"] {
  word-wrap: break-word;
  overflow-wrap: break-word;
  max-width: 100%;
  line-height: 1.4;
}

.toggle-btn {
  border: none;
  background-color: #007BFF;
  color: #fff;
  padding: 6px 12px;
  border-radius: 4px;
  cursor: pointer;
  font-size: 12px;
  font-weight: bold;
  box-shadow: 0 2px 6px rgba(0,0,0,0.2);
  transition: all 0.2s ease;
  display: none;
}

.toggle-btn:hover {
  background-color: #0056b3;
  transform: translateY(-1px);
  box-shadow: 0 3px 8px rgba(0,0,0,0.3);
}

.save-btn {
  border: none;
  background-color: #28a745;
  color: #fff;
  padding: 6px 12px;
  border-radius: 4px;
  cursor: pointer;
  font-size: 12px;
  font-weight: bold;
  box-shadow: 0 2px 6px rgba(0,0,0,0.2);
  transition: all 0.2s ease;
}

.save-btn:hover {
  background-color: #218838;
  transform: translateY(-1px);
  box-shadow: 0 3px 8px rgba(0,0,0,0.3);
}

.col3 button {
  border: none;
  background-color: #6c757d;
  color: #fff;
  padding: 8px 12px;
  border-radius: 4px;
  cursor: pointer;
  font-size: 12px;
  transition: all 0.2s ease;
}

.col3 button:hover {
  background-color: #5a6268;
  transform: translateY(-1px);
}

.editor-image img {
  width: 250px;
  height: 130px;
}

.editor-controls {
  margin-bottom: 10px;
}

.editor-controls button {
  margin-right: 10px;
}

button {
  margin: 0;
  outline: none;
}

button:active {
  transform: translateY(0);
}
