html {
  scroll-behavior: smooth;
}

body {
  font-family: Arial, sans-serif;
  margin: 0;
  padding: 20px;
  background-color: #f1f1f1;
}

.header {
  text-align: center;
  margin: 20px 0;
}

.title {
  font-size: 2em;
  font-weight: bold;
  color: #181818;
}

.page-count {
  font-size: 1.5em;
  margin-top: 10px;
  color: #181818;
}

.container {
  display: flex;
  flex-wrap: wrap;
  justify-content: center;
}

.tile {
  width: 360px;
  background-color: #eaeaea;
  margin: 10px;
  box-shadow: 0 2px 5px rgba(0,0,0,0.3);
  transition: background-color 0.3s;
}

.tile:hover {
  background-color: #d8f6ff;
}

.tile img {
  width: 100%;
  display: block;
}

.tile a {
  text-decoration: none;
  color: #636363;
  display: block;
}

.tile .caption {
  text-align: center;
  padding: 10px;
  font-size: 70%;
  color: inherit;
}

.footer {
  text-align: center;
  margin: 20px 0;
}

.footer a {
  margin: 0 10px;
  text-decoration: none;
  color: #000;
}

/* 上に戻るボタン */
#back-to-top {
  position: fixed;
  bottom: 30px;
  right: 30px;
  width: 50px;
  height: 50px;
  background-color: rgba(173,216,230,0.7);
  color: #fff;
  text-align: center;
  line-height: 50px;
  border-radius: 50%;
  text-decoration: none;
  font-size: 24px;
  display: none;
  z-index: 1000;
  transition: opacity 0.3s;
}

#back-to-top:hover {
  opacity: 0.9;
}
