#upload-container {
  max-width: 600px;
  margin: 50px auto;
  padding: 20px;
  background: #fff;
  border: 2px dashed #00bfff;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0,0,0,0.1);
  text-align: center;
}

#drop-area {
  border: 2px dashed #00bfff;
  border-radius: 20px;
  padding: 40px 20px;
  text-align: center;
  color: #234f9e;
  font-size: 130%;
  background-color: #f0fbff;
  transition: background-color 0.3s ease, border-color 0.3s ease;
}

#drop-area.hover {
  border-color: #009acd;
  background-color: #e0f7ff;
}

.select-file {
  text-decoration: underline;
  text-decoration-color: #6a81b6;
  cursor: pointer;
  transition: background-color 0.3s;
}

.select-file:hover {
  background-color: #00bfff;
}

.progress-overlay {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: rgba(0,0,0,0.5);
  display: none;
  justify-content: flex-start;
  padding-top: 40vh;
  align-items: center;
  z-index: 1000;
  flex-direction: column;
}

#progress-text {
  font-size: 2.2em;
  color: #ffffff;
  text-shadow: 2px 2px 4px rgba(0,0,0,0.5);
}

.spinner {
  border: 8px solid #f3f3f3;
  border-top: 8px solid #3498db;
  border-radius: 50%;
  width: 60px;
  height: 60px;
  animation: spin 1s linear infinite;
  margin-bottom: 20px;
  opacity: 0.6;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}
