events {
    worker_connections 1024;
}

http {
    include       /etc/nginx/mime.types;
    default_type  application/octet-stream;

    # ログ設定
    log_format main '$remote_addr - $remote_user [$time_local] "$request" '
                    '$status $body_bytes_sent "$http_referer" '
                    '"$http_user_agent" "$http_x_forwarded_for"';

    access_log /var/log/nginx/access.log main;
    error_log /var/log/nginx/error.log;

    # Gzip圧縮
    gzip on;
    gzip_vary on;
    gzip_min_length 1024;
    gzip_types text/plain text/css text/xml text/javascript application/javascript application/xml+rss application/json;

    # ファイルアップロードサイズ制限
    client_max_body_size 100M;
    
    # プロキシ設定
    proxy_connect_timeout 60s;
    proxy_send_timeout 60s;
    proxy_read_timeout 60s;

    # アップストリームFlaskアプリ
    upstream pdf_converter {
        server pdf-converter:5000;
    }

    server {
        listen 80;
        server_name localhost;

        # 静的ファイル用のルートディレクトリ
        root /var/www/html;
        index index.html;

        # Flaskアプリケーションルート
        location / {
            proxy_pass http://pdf_converter;
            proxy_set_header Host $host;
            proxy_set_header X-Real-IP $remote_addr;
            proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
            proxy_set_header X-Forwarded-Proto $scheme;
        }

        # 生成されたHTMLファイルを直接配信
        location /zip/html/ {
            alias /var/www/html/html/;
            try_files $uri $uri/ =404;
            
            # 動的生成コンテンツ（画像、HTML）のキャッシュ無効化
            # これらのファイルはPDFアップロードごとに再生成される
            location ~* \.(jpg|jpeg|png|gif|html|htm)$ {
                add_header Cache-Control "no-cache, no-store, must-revalidate";
                add_header Pragma "no-cache";
                add_header Expires "0";
            }
            
            # 静的アセット（CSS、JS）はキャッシュする
            location ~* \.(css|js|ico)$ {
                expires 1y;
                add_header Cache-Control "public, immutable";
            }
        }
        
        # ZIPダウンロードを配信
        location /zip/dl/ {
            alias /var/www/html/zip/dl/;
            try_files $uri $uri/ =404;
            
            # ZIPファイルダウンロード
            location ~* \.(zip)$ {
                add_header Content-Type application/zip;
                add_header Content-Disposition "attachment; filename=$1";
                expires 1h;
            }
        }

        # ヘルスチェックエンドポイント
        location /health {
            access_log off;
            return 200 "healthy\n";
            add_header Content-Type text/plain;
        }

        # エラーページ
        error_page 404 /404.html;
        error_page 500 502 503 504 /50x.html;
    }

    # HTTPSサーバー（オプション - SSL証明書がある場合はコメントアウト）
    # server {
    #     listen 443 ssl;
    #     server_name localhost;
    #     
    #     ssl_certificate /etc/nginx/ssl/cert.pem;
    #     ssl_certificate_key /etc/nginx/ssl/key.pem;
    #     
    #     # 上記HTTPサーバーと同じ設定
    #     root /var/www/html;
    #     index index.html;
    #     
    #     location / {
    #         proxy_pass http://pdf_converter;
    #         proxy_set_header Host $host;
    #         proxy_set_header X-Real-IP $remote_addr;
    #         proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
    #         proxy_set_header X-Forwarded-Proto $scheme;
    #     }
    # }
}
