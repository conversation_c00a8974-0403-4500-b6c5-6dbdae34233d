<div class="row">
  <div class="col col1">{number}</div>
  <div class="col col2">{img_html}</div>
  <div class="col col3"><button onclick="openEditor('{js_number}', '{js_img_path}')">編集</button></div>
  <div class="col col4" id="text-{number}">
     <div id="text-content-{number}" class="text-content" 
          onclick="makeEditable('{js_number}')" 
          onblur="makeReadonly('{js_number}')"
          contenteditable="false">{html_text_content}</div>
     <div class="controls left-controls">
        <button id="toggle-{number}" class="toggle-btn" onclick="toggleText('text-{number}', 'toggle-{number}')">＋</button>
     </div>
     <div class="controls right-controls">
        <button class="save-btn" onclick="saveText('{js_number}')">保存</button>
     </div>
  </div>
</div>
