<!DOCTYPE html>
<html lang="ja">
<head>
  <meta charset="UTF-8">
  <title>PDF→HTML変換</title>
  <link rel="stylesheet" href="/static/css/common.css">
  <link rel="stylesheet" href="/static/css/result.css">
  <script>
    function openPopup(url) {
      window.open(url, 'popup', 'width=1200,height=900');
    }
  </script>
</head>
<body>
  <header>
    <h1>
      <a href="/">PDF→HTML変換</a>
      <span>(プロトタイプ)</span>
    </h1>
  </header>
  <div id="result-container">
    {% if image_count > 0 %}
    <h2>変換結果</h2>
    <p>全 {{ image_count }} ページ</p>
    <p><a class="link" href="/job/{{ job_id }}/zip/dl/result.zip" download>{{ zip_filename }} {{ file_size }} MB</a></p>
    <button onclick="window.location.href='/job/{{ job_id }}/zip/dl/result.zip'" download>ZIPファイルをダウンロード</button>
    <button onclick="openPopup('/job/{{ job_id }}/html/1.html')">HTMLページを表示</button>
    <button onclick="openPopup('/job/{{ job_id }}/html/contents.html')">目次ページを表示</button>
    <button onclick="openPopup('/job/{{ job_id }}/html/edit.html')">テキスト編集画面</button>
    <p class="info-text">使用方法: ダウンロードしたZIPファイルを展開し、contents.html を開いて目次から始めるか、1.html を開いて最初のページから閲覧を開始してください。</p>
    <p class="info-text">ジョブ管理: このジョブ ({{ job_id }}) のファイルは24時間保存されます。その後自動的に削除されます。</p>
    <p><a href="/">TOPへ戻る</a></p>
    {% else %}
    <p>画像の変換が完了していません。しばらくしてからページを更新してください。</p>
    {% endif %}
  </div>
  <p class="info-text">※現在変換処理の途中で最終結果画面が表示される場合があります。<br>その場合は、数値や画像が正しく反映されるまでF5キーを押して更新願います。（原因調査中）</p>
</body>
</html>