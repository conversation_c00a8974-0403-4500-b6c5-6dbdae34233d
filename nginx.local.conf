worker_processes auto;

events {
    worker_connections 1024;
}

http {
    include       /etc/nginx/mime.types;
    default_type  application/octet-stream;

    sendfile on;
    tcp_nopush on;
    tcp_nodelay on;
    keepalive_timeout 65;

    # ログ設定
    log_format main '$remote_addr - $remote_user [$time_local] "$request" '
                    '$status $body_bytes_sent "$http_referer" '
                    '"$http_user_agent" "$http_x_forwarded_for"';

    access_log /var/log/nginx/access.log main;
    error_log /var/log/nginx/error.log;

    # Gzip圧縮
    gzip on;
    gzip_vary on;
    gzip_min_length 1024;
    gzip_types text/plain text/css text/xml text/javascript application/javascript application/xml+rss application/json;

    # ファイルアップロードサイズ制限
    client_max_body_size 100M;
    
    # プロキシ設定
    proxy_connect_timeout 60s;
    proxy_send_timeout 60s;
    proxy_read_timeout 60s;

    # アップストリームFlaskアプリ (Docker container name)
    upstream pdf_converter {
        server pdf-converter:5000;
    }

    server {
        listen 80;
        server_name localhost;

        location ~ ^/job/([^/]+)/(.*)$ {
            rewrite ^/job/([^/]+)/(.*)$ /$1/$2 break;
            root /var/www/html;
            
            # ZIPファイルの場合はダウンロードヘッダ追加
            if ($uri ~ \.zip$) {
                add_header Content-Type "application/zip";
                add_header Content-Disposition "attachment; filename=$2";
            }
            
            # HTMLファイルの場合はcharset指定
            if ($uri ~ \.html$) {
                add_header Content-Type "text/html; charset=utf-8";
            }
            
            # キャッシュ設定
            expires 1h;
            add_header Cache-Control "public";
        }
        
        # API proxy: update_text エンドポイント
        location ~ ^/update_text/(.*)$ {
            proxy_pass http://pdf-converter:5000/update_text/$1;
            proxy_set_header Host $host;
            proxy_set_header X-Real-IP $remote_addr;
            proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
            proxy_set_header X-Forwarded-Proto $scheme;
            proxy_read_timeout 60s;
            proxy_connect_timeout 10s;
        }
        
        # Flaskアプリケーション（その他すべて）
        location / {
            proxy_pass http://pdf-converter:5000;
            proxy_set_header Host $host;
            proxy_set_header X-Real-IP $remote_addr;
            proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
            proxy_set_header X-Forwarded-Proto $scheme;
            proxy_read_timeout 300s;
            proxy_connect_timeout 10s;
        }

        # エラーページ
        error_page 404 500 502 503 504 /error.html;
        location = /error.html {
            root /usr/share/nginx/html;
            internal;
        }
    }
}
