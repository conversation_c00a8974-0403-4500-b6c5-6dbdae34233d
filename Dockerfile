# Python 3.13 slimイメージを使用
FROM python:3.13-slim

# 作業ディレクトリを設定
WORKDIR /app

# PDF処理と画像操作用のシステム依存関係をインストール
RUN apt-get update && apt-get install -y \
    build-essential \
    python3-dev \
    python3-pil \
    libjpeg-dev \
    zlib1g-dev \
    libfreetype6-dev \
    libtiff5-dev \
    libopenjp2-7-dev \
    libwebp-dev \
    libharfbuzz-dev \
    libfribidi-dev \
    libxcb1-dev \
    libssl-dev \
    libffi-dev \
    curl \
    && rm -rf /var/lib/apt/lists/*

# より良いキャッシュのためにrequirementsを最初にコピー
COPY requirements.txt .

# Docker互換性のための特定バージョンでPython依存関係をインストール
RUN pip install --upgrade pip setuptools wheel

# requirements.txtから依存関係をインストール
RUN pip install -r requirements.txt

# アプリケーションコードをコピー
COPY . .

# 必要なディレクトリを作成
RUN mkdir -p /app/dl \
    && mkdir -p /app/output/html/image \
    && mkdir -p /app/output/html/txt \
    && mkdir -p /app/output/zip/dl

# 環境変数を設定
ENV FLASK_APP=app.py
ENV FLASK_ENV=production
ENV PYTHONPATH=/app

# ポートを公開
EXPOSE 5000

# ヘルスチェック
HEALTHCHECK --interval=30s --timeout=10s --start-period=5s --retries=3 \
    CMD curl -f http://localhost:5000/ || exit 1

# アプリケーションを実行
CMD ["python", "app.py"]
