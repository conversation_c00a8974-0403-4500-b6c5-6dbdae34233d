.PHONY: help local prod clean debug-volumes

# 使用法表示
help:
	@echo "Available commands:"
	@echo "  local   - ローカル開発環境 (Docker)"
	@echo "  prod    - 本番環境 (Flaskアプリ起動のみ)"
	@echo "  clean   - 出力ディレクトリクリーン"

# ローカル開発環境（Docker）
local:
	@echo "Starting local development with Docker..."
	@docker compose up --build

# 本番環境（Flaskアプリ起動のみ）
prod:
	@echo "Starting production Flask application..."
	@echo "Loading production environment variables..."
	@export $$(grep -v '^#' env.production | grep -v '^$$' | xargs) && python3 app.py

# 出力ディレクトリクリーン
clean:
	@echo "Cleaning output directories..."
	@rm -rf output/*/html/*.html output/*/html/image/* output/*/html/txt/* output/*/zip/dl/* 2>/dev/null || true
	@rm -rf dl/*.pdf 2>/dev/null || true
	@echo "Clean completed."

