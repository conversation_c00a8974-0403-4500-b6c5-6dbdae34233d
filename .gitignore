# Python
__pycache__/
*.py[cod]
*$py.class
*.so
.Python
build/
develop-eggs/
dist/
downloads/
eggs/
.eggs/
lib/
lib64/
parts/
sdist/
var/
wheels/
share/python-wheels/
*.egg-info/
.installed.cfg
*.egg
PIPFILE.lock

# 仮想環境
venv/
env/
.venv/
.env/
ENV/
env.bak/
venv.bak/

# Flask
instance/
.webassets-cache

# 環境変数
.env
.flaskenv

# IDE
.vscode/
.idea/
*.swp
*.swo
*~

# OS
.DS_Store
.DS_Store?
._*
.Spotlight-V100
.Trashes
ehthumbs.db
Thumbs.db

# ログ
*.log
pip-log.txt
pip-delete-this-directory.txt

# テスト
.tox/
.nox/
.coverage
.coverage.*
.cache
nosetests.xml
coverage.xml
*.cover
*.py,cover
.hypothesis/
.pytest_cache/
cover/

# PDFファイル（アップロードコンテンツ）
dl/
ppt/
txtp/
output/

# ディレクトリ構造は保持してコンテンツのみ無視
!dl/.gitkeep
!ppt/.gitkeep
!txtp/.gitkeep
!output/.gitkeep

# 生成された出力ファイル
output/

# 出力ディレクトリ構造は保持してコンテンツのみ無視
!output/.gitkeep
!output/html/.gitkeep
!output/html/image/.gitkeep
!output/html/txt/.gitkeep
!output/zip/.gitkeep

# Dockerボリュームとnginx
/var/www/html/
nginx/logs/
ssl/

# 生成されたコンテンツ（必要に応じて保持または無視）
# /var/www/html/zip/html/
# 生成ファイルを追跡したくない場合はコメントアウト:
# *.html
# image/
# txt/

# 一時ファイル
*.tmp
*.temp
*.bak
*.backup

# セキュリティ
*.key
*.pem
*.crt
*.p12
config.ini
secrets.json

# データベース
*.db
*.sqlite
*.sqlite3

# Jupyter Notebook
.ipynb_checkpoints

# pyenv
.python-version

# pipenv
Pipfile.lock

# PEP 582
__pypackages__/

# Celery
celerybeat-schedule
celerybeat.pid

# SageMath解析ファイル
*.sage.py

# Spyderプロジェクト設定
.spyderproject
.spyproject

# Ropeプロジェクト設定
.ropeproject

# mkdocsドキュメント
/site

# mypy
.mypy_cache/
.dmypy.json
dmypy.json

# Pyre型チェッカー
.pyre/

# pytype静的型解析
.pytype/
