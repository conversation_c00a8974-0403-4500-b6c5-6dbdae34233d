import os
import glob
import logging
from config import config

logger = logging.getLogger(__name__)

class FileService:    
    def __init__(self):
        self.allowed_extensions = config.ALLOWED_EXTENSIONS
        
    def allowed_file(self, filename):
        """ファイル名の拡張子がPDFかどうかを判定"""
        return '.' in filename and filename.rsplit('.', 1)[1].lower() in self.allowed_extensions

    def delete_files_in_dir(self, directory, pattern='*'):
        """指定ディレクトリ内のファイルを削除"""
        if not os.path.exists(directory):
            logger.info(f"Directory created: {directory}")
            os.makedirs(directory, exist_ok=True)
            return
        
        files = glob.glob(os.path.join(directory, pattern))
        logger.info(f"Files to delete: {len(files)} in {directory}")
        for f in files:
            if os.path.isfile(f):
                try:
                    os.remove(f)
                except Exception as e:
                    logger.warning(f"Failed to delete {f}: {e}")

    def get_python_executable(self):
        if os.getcwd() == '/home/<USER>' and os.path.exists('/home/<USER>/venv/bin/python'):
            return '/home/<USER>/venv/bin/python'
        return 'python3'
