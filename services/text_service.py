import os
import logging
from utils.path_utils import calculate_job_paths

logger = logging.getLogger(__name__)

class TextService:    
    def update_text_file(self, job_id, file_id, content, job_service, zip_service, processing_service):
        """テキストファイル更新"""
        if not job_service.job_exists(job_id):
            raise Exception("ジョブが見つかりません")
        
        if not file_id or content is None:
            raise Exception("不正なリクエストです")
        
        try:
            paths = calculate_job_paths(job_id)
            file_path = os.path.join(paths['txt_dir'], f"{file_id}.txt")
            
            temp_file_path = file_path + '.tmp'
            with open(temp_file_path, 'w', encoding='utf-8') as f:
                f.write(content)
            os.rename(temp_file_path, file_path)        
            zip_service.regenerate_html_and_zip(job_id, job_service, processing_service)
            
            return True
            
        except Exception as e:
            error_msg = f"保存に失敗しました: {str(e)}"
            logger.error(f"Job {job_id}: {error_msg}")
            raise Exception(error_msg)
