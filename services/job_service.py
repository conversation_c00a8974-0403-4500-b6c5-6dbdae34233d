import os
import time
import uuid
import shutil
import logging
from typing import Dict
from config import config
from utils.path_utils import calculate_job_paths

logger = logging.getLogger(__name__)

class JobService:
    def __init__(self, output_base_dir: str = None, upload_folder: str = None, job_ttl: int = 86400):
        self.output_base_dir = output_base_dir or config.OUTPUT_BASE_DIR
        self.upload_folder = upload_folder or config.UPLOAD_FOLDER
        self.job_ttl = job_ttl
        
    def generate_job_id(self) -> str:
        timestamp = int(time.time())
        uuid_part = str(uuid.uuid4())[:8]
        return f"{timestamp}_{uuid_part}"
    
    def create_job_directories(self, job_id: str) -> Dict[str, str]:
        paths = calculate_job_paths(job_id)
        
        directories = {
            'job_base': paths['job_base'],
            'html_dir': paths['html_dir'],
            'image_dir': paths['image_dir'],
            'txt_dir': paths['txt_dir'],
            'zip_dl_dir': paths['zip_dl_dir']
        }
        
        logger.info(f"Job {job_id}: Creating job directories")
        
        for dir_name, dir_path in directories.items():
            try:
                os.makedirs(dir_path, exist_ok=True)
            except PermissionError as e:
                logger.error(f"Job {job_id}: Permission denied creating directory {dir_name}: {e}")
                raise
            except OSError as e:
                logger.error(f"Job {job_id}: OS error creating directory {dir_name}: {e}")
                raise
        
        logger.info(f"Job {job_id}: All directories created successfully")
        return directories
    
    def job_exists(self, job_id: str) -> bool:
        paths = calculate_job_paths(job_id)
        return os.path.exists(paths['job_base'])
    
    def is_job_expired(self, job_id: str) -> bool:
        try:
            timestamp = int(job_id.split('_')[0])
            return (int(time.time()) - timestamp) > self.job_ttl
        except (ValueError, IndexError):
            return True
    

    def get_job_status(self, job_id: str) -> str:
        if not self.job_exists(job_id):
            return 'not_found'
        
        if self.is_job_expired(job_id):
            return 'expired'
        
        paths = calculate_job_paths(job_id)
        zip_path = os.path.join(paths['zip_dl_dir'], 'result.zip')
        
        if os.path.exists(zip_path) and os.path.getsize(zip_path) > 0:
            return 'completed'
        else:
            return 'processing'
    
    def cleanup_expired_jobs(self) -> int:
        logger.info("Starting cleanup of expired jobs")
        
        if not os.path.exists(self.output_base_dir):
            logger.info("Output base directory does not exist, no cleanup needed")
            return 0
            
        expired_count = 0
        current_time = int(time.time())
        
        try:
            for job_dir in os.listdir(self.output_base_dir):
                job_path = os.path.join(self.output_base_dir, job_dir)
                if not os.path.isdir(job_path):
                    continue
                    
                job_id = job_dir
                
                try:
                    timestamp = int(job_id.split('_')[0])
                    if current_time - timestamp > self.job_ttl:
                        self.delete_job(job_id)
                        expired_count += 1
                        logger.info(f"Expired job removed: {job_id}")
                except (ValueError, IndexError):
                    self.delete_job(job_id)
                    expired_count += 1
                    logger.info(f"Invalid job removed: {job_id}")
            
            logger.info(f"Cleanup completed: {expired_count} jobs removed")
            return expired_count
            
        except Exception as e:
            logger.error(f"Error during job cleanup: {e}")
            return expired_count
    
    def delete_job(self, job_id: str) -> None:
        logger.info(f"Job {job_id}: Deleting job")
        
        paths = calculate_job_paths(job_id)
        
        if os.path.exists(paths['job_base']):
            try:
                shutil.rmtree(paths['job_base'], ignore_errors=True)
                logger.info(f"Job {job_id}: Directory removed: {paths['job_base']}")
            except Exception as e:
                logger.error(f"Job {job_id}: Failed to remove directory {paths['job_base']}: {e}")
        
        if os.path.exists(paths['pdf_path']):
            try:
                os.remove(paths['pdf_path'])
                logger.info(f"Job {job_id}: PDF file removed: {paths['pdf_path']}")
            except OSError as e:
                logger.error(f"Job {job_id}: Failed to remove PDF file {paths['pdf_path']}: {e}")
        
        logger.info(f"Job {job_id}: Job deletion completed")
    
    def validate_job_id(self, job_id: str) -> bool:
        if not job_id or len(job_id) < 10 or '_' not in job_id:
            return False
        
        parts = job_id.split('_')
        if len(parts) != 2:
            return False
        
        timestamp_part = parts[0]
        return timestamp_part.isdigit() and len(timestamp_part) == 10
