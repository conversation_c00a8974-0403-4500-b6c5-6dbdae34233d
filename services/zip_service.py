import os
import zipfile
import time
import subprocess
import logging
from utils.path_utils import calculate_job_paths

logger = logging.getLogger(__name__)

class ZipService:    
    def create_zip_file(self, job_id):
        paths = calculate_job_paths(job_id)
        zip_path = os.path.join(paths['zip_dl_dir'], 'result.zip')
        
        with zipfile.ZipFile(zip_path, 'w', zipfile.ZIP_DEFLATED) as zipf:
            for root, dirs, files in os.walk(paths['html_dir']):
                for file_name in files:
                    file_path = os.path.join(root, file_name)
                    arcname = os.path.relpath(file_path, paths['html_dir'])
                    zipf.write(file_path, arcname)
        
        return zip_path

    def regenerate_html_and_zip(self, job_id, job_service, processing_service):
        """HTMLとZIPファイル再生成"""
        try:
            if not job_service.job_exists(job_id):
                logger.error(f"Job {job_id}: Job not found")
                return False
                
            paths = calculate_job_paths(job_id)
            
            logger.info(f"Job {job_id}: Regenerating HTML and ZIP files...")
            
            for script in ['h.py', 'edit.py']:
                try:
                    processing_service.run_script(job_id, script)
                    logger.info(f"Job {job_id}: {script} completed successfully")
                except subprocess.TimeoutExpired:
                    logger.warning(f"Job {job_id}: {script} timeout")
                except Exception as e:
                    logger.error(f"Job {job_id}: {script} failed: {str(e)}")
            
            zip_path = os.path.join(paths['zip_dl_dir'], 'result.zip')
            temp_zip_path = zip_path + '.tmp'
            
            with zipfile.ZipFile(temp_zip_path, 'w', zipfile.ZIP_DEFLATED, compresslevel=6) as zipf:
                for root, dirs, files in os.walk(paths['html_dir']):
                    for file_name in files:
                        file_path = os.path.join(root, file_name)
                        arcname = os.path.relpath(file_path, paths['html_dir'])
                        zipf.write(file_path, arcname)
            
            if os.path.exists(zip_path):
                os.remove(zip_path)
            os.rename(temp_zip_path, zip_path)
            
            file_size_bytes = 0
            for _ in range(10):
                file_size_bytes = os.path.getsize(zip_path)
                if file_size_bytes > 0:
                    break
                time.sleep(0.1)

            if file_size_bytes == 0:
                logger.error(f"Job {job_id}: ZIP file recreation failed - file size is 0")
                return False

            file_size_mb = round(file_size_bytes / (1024 * 1024), 2)
            logger.info(f"Job {job_id}: HTML and ZIP regeneration completed. ZIP size: {file_size_mb} MB")
            return True
            
        except Exception as e:
            if 'temp_zip_path' in locals() and os.path.exists(temp_zip_path):
                os.remove(temp_zip_path)
            
            logger.error(f"Job {job_id}: Error during HTML/ZIP regeneration: {str(e)}")
            return False
