import os
import subprocess
import time
import logging
from config import config
from utils.path_utils import calculate_job_paths

logger = logging.getLogger(__name__)

class ProcessingService:    
    def __init__(self, python_executable):
        self.python_executable = python_executable
        self.scripts_dir = config.SCRIPTS_DIR
        
    def run_script(self, job_id, script_name, timeout=300):
        """Execute a script for the given job"""
        logger.info(f"Job {job_id}: Running {script_name}...")
        result = subprocess.run([self.python_executable, os.path.join(self.scripts_dir, script_name), job_id], 
                              capture_output=True, text=True, timeout=timeout)
        if result.returncode != 0:
            raise Exception(f"{script_name} failed: {result.stderr}")
        logger.info(f"Job {job_id}: {script_name} completed")

    def wait_for_file_counts_match(self, job_id, max_retries=180):
        """テキストと画像ファイル数の一致確認"""
        paths = calculate_job_paths(job_id)
        txt_folder = paths['txt_dir']
        img_folder = paths['image_dir']
        
        retries = 0
        while retries < max_retries:
            try:
                txt_count = len([f for f in os.listdir(txt_folder) if f.lower().endswith('.txt')])
                img_count = len([f for f in os.listdir(img_folder) if f.lower().endswith('.jpg')])
            except Exception:
                txt_count, img_count = 0, 0
            if txt_count == img_count and txt_count != 0:
                return True
            time.sleep(1)
            retries += 1
        return False

    def process_files(self, job_id, job_service):
        """PDFファイル処理実行"""
        try:
            if not job_service.job_exists(job_id):
                logger.error(f"Job {job_id}: Job not found")
                return
                
            paths = calculate_job_paths(job_id)
            
            logger.info(f"Job {job_id}: PDF processing started...")
            
            # 1. t.py実行
            self.run_script(job_id, 't.py')

            # 2. contents.py実行
            self.run_script(job_id, 'contents.py')
            
            # 3. i.py実行
            self.run_script(job_id, 'i.py', timeout=600)
            
            time.sleep(1)
            if not self.wait_for_file_counts_match(job_id):
                raise Exception("i.py processing failed - text file count and image count do not match")

            # 4. h.py実行
            self.run_script(job_id, 'h.py')
            
            # 5. thumbnail.py実行
            self.run_script(job_id, 'thumbnail.py')

            # 6. ZIP作成
            logger.info(f"Job {job_id}: Creating ZIP file...")
            # Import locally to avoid circular import
            import zipfile
            paths = calculate_job_paths(job_id)
            zip_path = os.path.join(paths['zip_dl_dir'], 'result.zip')
            
            with zipfile.ZipFile(zip_path, 'w', zipfile.ZIP_DEFLATED) as zipf:
                for root, dirs, files in os.walk(paths['html_dir']):
                    for file_name in files:
                        file_path = os.path.join(root, file_name)
                        arcname = os.path.relpath(file_path, paths['html_dir'])
                        zipf.write(file_path, arcname)
            
            # 7. edit.py実行
            try:
                self.run_script(job_id, 'edit.py')
            except Exception as e:
                logger.warning(f"edit.py failed but continuing: {e}")
            
            # ZIPファイルサイズ確認とログ出力
            file_size_bytes = 0
            for _ in range(30):
                file_size_bytes = os.path.getsize(zip_path)
                if file_size_bytes > 0:
                    break
                time.sleep(1)
            
            if file_size_bytes == 0:
                raise Exception("ZIP file creation failed - file size is 0")
            
            file_size_mb = round(file_size_bytes / (1024 * 1024), 2)
            logger.info(f"Job {job_id} completed. ZIP size: {file_size_mb} MB")
            
        except subprocess.TimeoutExpired as e:
            logger.error(f"Job {job_id}: Script timeout: {e}")
        except subprocess.CalledProcessError as e:
            logger.error(f"Job {job_id}: Script execution error: {e}")
        except Exception as e:
            logger.error(f"Job {job_id}: General error: {e}")
