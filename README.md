# PDF → HTML コンバーター (プロトタイプ)

## 概要
小さな Flask アプリケーションです。アップロードされた PDF を画像・テキストに分解し、目次やサムネイルを自動生成して 1 つの HTML サイトにまとめ、ZIP にパッケージします。Python ロジックは本番／開発で共通で、異なるのは入出力パスのみです。

## 動作環境
| 環境 | 起動コマンド | URL | 設定ファイル |
|------|--------------|-----|--------------|
| ローカル (Docker-Compose) | `make local` | http://localhost | `env.local` |
| 本番 (Ubuntu) | `make prod` | http://<server-ip> | `env.production` |

※ 本番環境では既存のnginxを使用し、アプリケーションはポート5000で起動します。

## 使い方 (ローカル)
```bash
make local       # コンテナ起動 (pdf-converter + nginx)
open http://localhost
```
アップロード後、進捗 100% で ZIP が生成され、プレビューも確認できます。

## ディレクトリ構造 (実行時)
```text
<session>/                 # 例: 1735123456_a1b2c3d4
├── html/
│   ├── image/             # ページ画像 (JPEG)
│   ├── txt/               # 抽出テキスト
│   ├── contents.html      # 目次
│   ├── thumbnail.html     # サムネイル一覧
│   ├── edit.html          # テキスト編集 UI
│   └── *.html             # 各ページ
└── zip/dl/result.zip      # 上記をまとめた ZIP
```
セッションは 24 時間後に自動削除されます。

## 主要コマンド
* `make local`  – ローカル開発用 Docker スタック起動  
* `make prod`   – `venv/` を有効化し Flask を直接起動  
* `make clean`  – 古い `output/` と `dl/` を削除

## 構成ファイル
| ファイル | 説明 |
|---------|------|
| `env.production` | 本番用環境変数（パス設定含む） |
| `env.local` | ローカル開発用環境変数（Docker用） |
| `docker-compose.yml` | ローカル開発用 2 コンテナ定義 |
| `nginx.local.conf` | Docker 内 nginx 用設定 |

## デプロイメント手順（本番）
1. ソースコードをzipにする
2. 本番環境で既存コードに上書き
3. `make prod` でアプリケーション起動
4. 既存nginxが自動的に `/var/www/html/zip` 配下を配信

**注意**: 本番環境の nginx 設定やディレクトリ権限は既に設定済みのため、変更不要です。

## ガイドライン
* コメントは日本語、ログは英語  
* 上位ディレクトリ (`/var/www/html/zip`, `/home/<USER>/pdfapp/dl`) には触れません。  
* Python 3.13 / Flask 3.1 ベース – 依存関係は `requirements.txt` を参照。

--- 
