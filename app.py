import os
import time
import threading
import logging
from flask import Flask, render_template, request, jsonify, make_response, send_from_directory
from config import config
from services.job_service import JobService
from utils.path_utils import calculate_job_paths
from services.file_service import FileService
from services.processing_service import ProcessingService
from services.zip_service import ZipService
from services.text_service import TextService

# ログ設定
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

app = Flask(__name__)

# 環境設定（自動検出）
app.config.from_object(config)
app.secret_key = config.SECRET_KEY

# 設定値取得
UPLOAD_FOLDER = config.UPLOAD_FOLDER
ALLOWED_EXTENSIONS = config.ALLOWED_EXTENSIONS
OUTPUT_BASE_DIR = config.OUTPUT_BASE_DIR
SCRIPTS_DIR = config.SCRIPTS_DIR

# 現在の環境とパスをログ出力
logger.info(f"Environment: {os.getenv('FLASK_CONFIG', 'auto-detected')}")
logger.info(f"Upload folder: {UPLOAD_FOLDER}")
logger.info(f"Output base directory: {OUTPUT_BASE_DIR}")

job_service = JobService(OUTPUT_BASE_DIR, UPLOAD_FOLDER)

file_service = FileService()
PYTHON_EXECUTABLE = file_service.get_python_executable()
processing_service = ProcessingService(PYTHON_EXECUTABLE)
zip_service = ZipService()
text_service = TextService()

logger.info(f"Python executable: {PYTHON_EXECUTABLE}")

app.config['UPLOAD_FOLDER'] = UPLOAD_FOLDER

def cleanup_old_jobs():
    """期限切れジョブの削除"""
    return job_service.cleanup_expired_jobs()

@app.route('/', methods=['GET'])
def index():
    """トップページ表示"""
    cleanup_old_jobs()
    return render_template('index.html')

@app.route('/upload', methods=['POST'])
def upload():
    """PDFファイルアップロード処理（ジョブ別）"""
    if 'file' not in request.files:
        return jsonify({"error": "ファイルが選択されていません"}), 400
    
    file = request.files['file']
    if file.filename == '':
        return jsonify({"error": "アップロードするファイルがありません"}), 400
    
    if not (file and file_service.allowed_file(file.filename)):
        return jsonify({"error": "PDFファイルのみアップロード可能です"}), 400

    try:
        job_id = job_service.generate_job_id()
        paths = calculate_job_paths(job_id)
        
        file.save(paths['pdf_path'])
        job_service.create_job_directories(job_id)
        
        threading.Thread(target=processing_service.process_files, args=(job_id, job_service)).start()
        
        return jsonify({"status": "started", "job_id": job_id})
        
    except Exception as e:
        logger.error(f"Upload failed: {e}")
        return jsonify({"error": "アップロードに失敗しました"}), 500

@app.route('/status/<job_id>', methods=['GET'])
def get_status(job_id):
    """ジョブステータス取得"""
    if not job_service.validate_job_id(job_id):
        return jsonify({"error": "無効なジョブIDです"}), 400
    
    try:
        status = job_service.get_job_status(job_id)
        
        if status == 'not_found':
            return jsonify({"error": "ジョブが見つかりません"}), 404
        elif status == 'expired':
            return jsonify({"error": "ジョブが期限切れです"}), 410
        
        return jsonify({"status": status, "job_id": job_id})
        
    except Exception as e:
        logger.error(f"Job {job_id}: Error in status check: {e}")
        return jsonify({"error": "ステータス取得に失敗しました"}), 500

@app.route('/result/<job_id>', methods=['GET'])
def result(job_id):
    """ジョブ結果表示"""
    if not job_service.validate_job_id(job_id):
        return jsonify({"error": "無効なジョブIDです"}), 400
    
    try:
        status = job_service.get_job_status(job_id)
        
        if status == 'not_found':
            return jsonify({"error": "ジョブが見つかりません"}), 404
        elif status == 'expired':
            return jsonify({"error": "ジョブが期限切れです"}), 410
        elif status == 'processing':
            return jsonify({"status": "processing", "message": "処理中です"}), 202
        
        paths = calculate_job_paths(job_id)
        zip_path = os.path.join(paths['zip_dl_dir'], 'result.zip')
        
        file_size_mb = 0
        if os.path.exists(zip_path):
            file_size_bytes = os.path.getsize(zip_path)
            file_size_mb = round(file_size_bytes / (1024 * 1024), 2)
        
        try:
            image_folder = paths['image_dir']
            image_count = len([f for f in os.listdir(image_folder) if f.lower().endswith('.jpg')])
        except Exception:
            image_count = 0
        
        response = make_response(render_template('result.html',
                               zip_filename="result.zip",
                               file_size=file_size_mb,
                               image_count=image_count,
                               timestamp=int(time.time() * 1000),
                               job_id=job_id))
        
        response.headers['Cache-Control'] = 'no-cache, no-store, must-revalidate'
        return response
        
    except Exception as e:
        logger.error(f"Job {job_id}: Error in result: {e}")
        return jsonify({"error": "結果取得に失敗しました"}), 500

@app.route('/job/<job_id>/<path:filename>')
def serve_job_file(job_id, filename):
    """ジョブファイル配信"""
    if not job_service.validate_job_id(job_id):
        return jsonify({"error": "無効なジョブIDです"}), 400
    
    safe_path = os.path.normpath(filename)
    if '..' in safe_path or safe_path.startswith('/'):
        return jsonify({"error": "不正なパスです"}), 400
    
    paths = calculate_job_paths(job_id)
    job_base_dir = paths['job_base']
    
    try:
        response = make_response(send_from_directory(job_base_dir, filename))
        return response
    except FileNotFoundError:
        return jsonify({"error": "ファイルが見つかりません"}), 404
    except PermissionError:
        return jsonify({"error": "アクセス権限がありません"}), 403
    except Exception as e:
        logger.error(f"Job {job_id}: Error serving file {filename}: {e}")
        return jsonify({"error": "ファイル配信に失敗しました"}), 500

@app.route('/update_text/<job_id>', methods=['POST'])
def update_text(job_id):
    """テキストファイル更新"""
    file_id = request.form.get('id')
    content = request.form.get('content')
    
    try:
        text_service.update_text_file(job_id, file_id, content, job_service, zip_service, processing_service)
        return jsonify({"success": True, "message": "テキストは保存されました。"})
        
    except Exception as e:
        error_msg = f"保存に失敗しました: {str(e)}"
        logger.error(f"Job {job_id}: {error_msg}")
        return jsonify({"error": error_msg}), 500

# CORS対応: localhost からの API アクセスを許可
@app.after_request
def after_request(response):
    response.headers.add('Access-Control-Allow-Origin', '*')
    response.headers.add('Access-Control-Allow-Headers', 'Content-Type,Authorization')
    response.headers.add('Access-Control-Allow-Methods', 'GET,PUT,POST,DELETE,OPTIONS')
    return response

if __name__ == '__main__':
    # 必要ディレクトリ作成
    os.makedirs(UPLOAD_FOLDER, exist_ok=True)
    os.makedirs(OUTPUT_BASE_DIR, exist_ok=True)
    
    # Docker環境では全インターフェースでリッスン
    app.run(host='0.0.0.0', port=5000, debug=False)
