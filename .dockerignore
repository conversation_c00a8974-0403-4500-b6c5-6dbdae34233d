# PDF-HTMLコンバーター用Dockerignoreファイル
# Dockerビルドコンテキストに含めるべきでないファイルとディレクトリを無視

# Python
__pycache__/
*.py[cod]
*$py.class
*.so
.Python
build/
develop-eggs/
dist/
downloads/
eggs/
.eggs/
lib/
lib64/
parts/
sdist/
var/
wheels/
*.egg-info/
.installed.cfg
*.egg
MANIFEST

# 仮想環境
pdf_env/
venv/
env/
ENV/

# IDE
.vscode/
.idea/
*.swp
*.swo
*~

# OS
.DS_Store
.DS_Store?
._*
.Spotlight-V100
.Trashes
ehthumbs.db
Thumbs.db

# ログ
*.log
logs/

# 出力ディレクトリ（ボリュームとしてマウントされる）
output/
dl/
static/

# SSL証明書（別途マウントする必要がある）
ssl/

# Git
.git/
.gitignore

# Docker
.dockerignore
docker-compose.override.yml

# 一時ファイル
tmp/
temp/
*.tmp

# 大きなファイル
*.pdf
*.zip

# ローカル設定
.env
.env.local
